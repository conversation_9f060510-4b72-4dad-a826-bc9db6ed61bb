import axios from 'axios'

/**
 * 根据环境配置请求文件资源
 * @param {string} filePath - 文件路径（不包含域名）
 * @param {object} options - 请求配置选项
 * @returns {Promise} axios请求Promise
 */
export function requestFile(filePath, options = {}) {
  let requestUrl

  // 智能判断是否使用代理
  const isLocalDev = process.env.VUE_APP_ENV === 'development' ||
                    window.location.hostname === 'localhost' ||
                    window.location.hostname === '127.0.0.1'

  if (isLocalDev) {
    // 本地开发环境使用代理
    requestUrl = filePath
    console.log('使用代理请求文件:', requestUrl)
  } else {
    // 部署环境直接请求完整URL
    const geoserverBase = process.env.VUE_APP_GEOSERVER_BASE || 'http://*************:8088/downloads'

    // 处理路径，避免重复的downloads
    let cleanPath = filePath.startsWith('/') ? filePath.substring(1) : filePath
    if (cleanPath.startsWith('downloads/')) {
      cleanPath = cleanPath.substring('downloads/'.length)
    }

    // 确保geoserverBase正确拼接
    const baseUrl = geoserverBase.endsWith('/downloads') ? geoserverBase : `${geoserverBase}/downloads`
    requestUrl = `${baseUrl}/${cleanPath}`
    console.log('直接请求文件URL:', requestUrl)
  }

  const defaultConfig = {
    timeout: 120 * 1000,
    headers: {
      'Accept': 'application/json, text/plain, */*'
    }
  }

  const requestConfig = { ...defaultConfig, ...options }

  return axios.get(requestUrl, requestConfig)
}

/**
 * 请求JSON文件并验证响应格式
 * @param {string} filePath - JSON文件路径
 * @param {object} fallbackData - 失败时的备用数据
 * @param {object} options - 请求配置选项
 * @returns {Promise} 返回JSON数据的Promise
 */
export function requestJsonFile(filePath, fallbackData = null, options = {}) {
  return requestFile(filePath, options)
    .then(res => {
      console.log('JSON file response:', res)
      
      // 检查响应内容类型，确保是JSON数据
      const contentType = res.headers['content-type'] || ''
      if (contentType.includes('application/json')) {
        return res.data
      } else {
        console.warn('响应不是JSON格式，content-type:', contentType)
        if (fallbackData !== null) {
          console.warn('使用备用数据')
          return fallbackData
        } else {
          throw new Error('响应不是有效的JSON格式')
        }
      }
    })
    .catch(err => {
      console.error('请求JSON文件失败:', err)
      if (fallbackData !== null) {
        console.warn('使用备用数据')
        return fallbackData
      } else {
        throw err
      }
    })
}
